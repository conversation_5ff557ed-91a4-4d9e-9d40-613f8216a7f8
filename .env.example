# LLM API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENAI_API_KEY=your_openai_api_key

# App Configuration
DEBUG=true
APP_NAME="Agents Backend"
API_PREFIX="/api/v1"

# Production AWS/S3 Configuration (only needed when DEBUG=false)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
BUCKET=your_s3_bucket_name
# S3_ENDPOINT_URL=  # Leave empty for AWS S3, set for custom S3-compatible services

# Production Redis Configuration (only needed when DEBUG=false)
REDIS_HOST=localhost
REDIS_PORT=6379
