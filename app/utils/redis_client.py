import redis
import os
from app.utils.common import is_development_mode


class RedisConfig:
    """Base Redis configuration class"""
    pass


class DevelopmentRedisConfig(RedisConfig):
    """Development Redis configuration with local services"""
    
    def __init__(self):
        self.HOST = "localhost"
        self.PORT = 6379


class ProductionRedisConfig(RedisConfig):
    """Production Redis configuration using environment variables"""
    
    def __init__(self):
        self.HOST = os.getenv("REDIS_HOST", "localhost")
        self.PORT = int(os.getenv("REDIS_PORT", "6379"))


def get_redis_config():
    """Get Redis configuration based on environment"""
    return DevelopmentRedisConfig() if is_development_mode() else ProductionRedisConfig()


def create_redis_client(config=None):
    """Create and return a Redis client based on configuration"""
    if config is None:
        config = get_redis_config()
    
    return redis.Redis(
        host=config.HOST,
        port=config.PORT,
        decode_responses=True
    )


# Initialize default Redis client
redis_config = get_redis_config()
redis_client = create_redis_client(redis_config)
