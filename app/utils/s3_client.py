import boto3
import os
from app.utils.common import is_development_mode


class S3Config:
    """Base S3 configuration class"""

    def __init__(self):
        self.S3_BASE = "naavix"


class DevelopmentS3Config(S3Config):
    """Development S3 configuration with local services"""

    def __init__(self):
        super().__init__()
        self.ENDPOINT_URL = "http://localhost:4566"
        self.AWS_ACCESS_KEY_ID = "test"
        self.AWS_SECRET_ACCESS_KEY = "test"
        self.AWS_REGION = "us-east-1"
        self.BUCKET = "my-local-bucket"


class ProductionS3Config(S3Config):
    """Production S3 configuration using environment variables"""

    def __init__(self):
        super().__init__()
        self.AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
        self.AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
        self.BUCKET = os.getenv("BUCKET")


def get_s3_config():
    """Get S3 configuration based on environment"""
    return DevelopmentS3Config() if is_development_mode() else ProductionS3Config()


def create_s3_client(config=None):
    """Create and return an S3 client based on configuration"""
    if config is None:
        config = get_s3_config()

    s3_client_kwargs = {
        "aws_access_key_id": config.AWS_ACCESS_KEY_ID,
        "aws_secret_access_key": config.AWS_SECRET_ACCESS_KEY,
        "region_name": config.AWS_REGION,
    }

    # Add endpoint_url only if specified (for local development)
    if config.ENDPOINT_URL:
        s3_client_kwargs["endpoint_url"] = config.ENDPOINT_URL

    return boto3.client("s3", **s3_client_kwargs)


# Initialize default S3 client and config
s3_config = get_s3_config()
s3_client = create_s3_client(s3_config)

# Export commonly used values
BUCKET = s3_config.BUCKET
S3_BASE = s3_config.S3_BASE
