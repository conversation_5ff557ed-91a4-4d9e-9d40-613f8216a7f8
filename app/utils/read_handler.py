from pathlib import Path
from typing import List, Optional, <PERSON><PERSON>
from typing import Dict
import os
from app.utils.logger import log_pretty
from app.utils.s3_client import s3_client as s3, BUCKET, S3_BASE
from app.utils.redis_client import redis_client as r

LOCAL_BASE = "data"


def get_module_required_suffixes(module_name: str) -> list[str]:
    """Get required file suffixes based on module name."""
    module_suffixes = {
        "terra": [".db"],
        "pm": [".db"],
        "forms": [".db"],
        # Add more modules as needed
    }
    return module_suffixes.get(module_name, [".db"])


def sync_s3_directory(
    module_name, org_id, resource_type, resource_id, required_suffixes: list[str] = None
):
    # Use module-based suffixes if not provided
    if required_suffixes is None:
        required_suffixes = get_module_required_suffixes(module_name)

    prefix = f"{S3_BASE}/{module_name}/{org_id}/{resource_type}/{resource_id}/"
    local_base = f"{LOCAL_BASE}/{module_name}/{org_id}/{resource_type}/{resource_id}"

    os.makedirs(local_base, exist_ok=True)

    # List all objects in the given prefix
    response = s3.list_objects_v2(Bucket=BUCKET, Prefix=prefix)

    if "Contents" not in response:
        raise FileNotFoundError(f"No files found at S3 prefix: {prefix}")

    files_synced = []
    found_suffixes = set()

    for obj in response["Contents"]:
        s3_key = obj["Key"]
        filename = s3_key.split("/")[-1]

        if not filename:
            continue

        local_path = os.path.join(local_base, filename)
        etag = obj["ETag"].strip('"')
        etag_key = (
            f"etag:{module_name}:{org_id}:{resource_type}:{resource_id}:{filename}"
        )

        # Check if the file needs download
        if not os.path.exists(local_path):
            # File doesn't exist locally, download it
            s3.download_file(BUCKET, s3_key, local_path)
            r.set(etag_key, etag)
            status = "downloaded"
        else:
            # File exists locally, check etag to see if it's up to date
            cached_etag = r.get(etag_key)
            if cached_etag != etag:
                s3.download_file(BUCKET, s3_key, local_path)
                r.set(etag_key, etag)
                status = "downloaded"
            else:
                status = "up_to_date"

        files_synced.append({
            "filename": filename,
            "etag": etag,
            "status": status,
            "local_path": local_path,
        })

        # Track suffixes found
        for suffix in required_suffixes:
            if filename.endswith(suffix):
                found_suffixes.add(suffix)

    # Ensure required suffixes are present
    missing_suffixes = [s for s in required_suffixes if s not in found_suffixes]
    if missing_suffixes:
        raise ValueError(f"Missing required files with suffixes: {missing_suffixes}")

    return {
        "status": "success",
        "prefix": prefix,
        "local_base": local_base,
        "synced_files": files_synced,
    }


def check_data_files_exist(org_id: str, module_name: str, request: Dict) -> bool:
    """
    Check if required data files exist in the specified directory path.
    Syncs from S3 first, then verifies local files exist.

    Args:
        org_id (str): The organization ID
        module_name (str): The module name
        request (dict): The request object containing additional parameters

    Returns:
        bool: True if at least one data file exists, False otherwise

    Raises:
        FileNotFoundError: If S3 sync fails due to missing files
        ValueError: If required file suffixes are missing
        Exception: For other S3 sync errors
    """
    # Extract resource information
    resource_type = request["resource_type"]
    resource_id = request["resource_id"]

    # Sync from S3 first - this will raise exceptions if there are issues
    try:
        result = sync_s3_directory(module_name, org_id, resource_type, resource_id)
        log_pretty("S3 Sync Result", result)
    except (FileNotFoundError, ValueError) as e:
        # Re-raise S3 sync errors to caller
        raise e
    except Exception as e:
        # Re-raise any other S3 sync errors
        raise Exception(f"S3 sync failed: {str(e)}") from e

    # Construct the base directory path
    dir_path = Path(f"data/{org_id}/{module_name}")
    print(f"Checking for data files in: {dir_path}")

    # Append resource-specific paths based on module
    if resource_type and resource_id and module_name in {"pm"}:
        dir_path = dir_path / f"{resource_type}/{resource_id}"
        print(f"Resource-specific path: {dir_path}")

    if resource_id and module_name in {"forms"}:
        dir_path = dir_path / resource_id
        print(f"Resource-specific path: {dir_path}")

    # Verify the directory exists
    if not dir_path.exists() or not dir_path.is_dir():
        print("Directory does not exist or is not a directory.")
        return False

    # Get required suffixes for this module
    required_suffixes = get_module_required_suffixes(module_name)

    # Look for valid data files
    for file in dir_path.iterdir():
        if file.is_file() and file.suffix.lower() in {
            suffix.lower() for suffix in required_suffixes
        }:
            print(f"Found data file: {file.name}")
            return True

    print("No data files found.")
    return False


def get_data_files(org_id: str, module_name: str) -> Tuple[List[str], List[str]]:
    """
    Get lists of .db and .csv files in the specified organization and module directory.

    Args:
        org_id (str): The organization ID
        module_name (str): The module name

    Returns:
        Tuple[List[str], List[str]]: A tuple containing two lists:
            - List of .db file paths
            - List of .csv file paths
    """
    db_files = []
    csv_files = []

    # Construct the directory path
    dir_path = Path(f"data/{org_id}/{module_name}")

    # Check if the directory exists
    if not dir_path.exists() or not dir_path.is_dir():
        return db_files, csv_files

    # Look for .db and .csv files
    for file in dir_path.iterdir():
        if file.is_file():
            if file.suffix.lower() == ".db":
                db_files.append(str(file))
            elif file.suffix.lower() == ".csv":
                db_files.append(str(file))
            elif file.suffix.lower() == ".duckdb":
                csv_files.append(str(file))

    return db_files, csv_files


def get_database_path(org_id: str, module_name: str) -> Optional[str]:
    """
    Get the path to the first .db file found in the specified organization and module directory.

    Args:
        org_id (str): The organization ID
        module_name (str): The module name

    Returns:
        Optional[str]: Path to the first .db file found, or None if no .db file exists
    """
    # Construct the directory path
    dir_path = Path(f"data/{org_id}/{module_name}")

    # Check if the directory exists
    if not dir_path.exists() or not dir_path.is_dir():
        return None

    # Look for .db files
    for file in dir_path.iterdir():
        if file.is_file() and file.suffix.lower() == ".db":
            return str(file)

    # No .db file found
    return None
