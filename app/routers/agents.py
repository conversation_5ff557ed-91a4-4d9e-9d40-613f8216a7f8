import json
import logging
import time
import threading
from typing import Any, Dict, Optional
from concurrent.futures import ThreadPoolExecutor
from fastapi import API<PERSON><PERSON>er, Body, HTTPException, Path, Query, Request
from fastapi.responses import StreamingResponse
from google.adk.agents.run_config import RunConfig
from google.adk.events import Event, EventActions
from google.adk.runners import Runner
from google.genai import types
from litellm import (
    APIError,
    InvalidRequestError,
    RateLimitError,
    ServiceUnavailableError,
    InternalServerError,
    Timeout,
    completion,
)
from pydantic import BaseModel
from agents.common.suggestions import get_related_questions
from app.utils.logger import log_pretty
from app.utils.agents import agents
from app.utils.event_processor import process_agent_events, process_session_events
from app.utils.read_handler import check_data_files_exist
from app.utils.session import SessionService
from app.utils.token_usage import update_usage_metadata
import asyncio

STOPPED = object()
# ────────────────────────── Config ──────────────────────────
HEARTBEAT = 15  # seconds between keep-alive comments
QUEUE_SIZE = 128  # max buffered ADK events per chat
WORKERS = 32  # threads for concurrent ADK runs
# ─────────────────────────────────────────────────────────────
logger = logging.getLogger(__name__)
executor = ThreadPoolExecutor(max_workers=WORKERS)
router = APIRouter(prefix="/ai", tags=["Agents"])


## All these would be moved to a separate file later
def adk_worker(
    session_id: str,
    query: str,
    loop: asyncio.AbstractEventLoop,
    q: asyncio.Queue,
    runner: Runner,
    user_id,
    stop_flag: threading.Event,
):
    """
    Runs in a thread-pool worker.
    Feeds processed ADK events back to the main loop via `q`.
    """
    logger.info(f"ADK worker started for session {session_id}")

    async def _run():
        content = types.Content(role="user", parts=[types.Part(text=query)])
        try:
            async for evt in runner.run_async(
                user_id=user_id,
                session_id=session_id,
                new_message=content,
                run_config=RunConfig(streaming_mode="sse"),
            ):
                if stop_flag.is_set():
                    break
                # back-pressure: wait if queue is full
                while True:
                    try:
                        loop.call_soon_threadsafe(
                            q.put_nowait, process_agent_events(evt)
                        )
                        break
                    except asyncio.QueueFull:
                        await asyncio.sleep(0.05)

        except RateLimitError as e:
            logger.warning(f"Rate limit hit: {str(e)}")
            error_data = {
                "event": "error",
                "content": "Rate limit exceeded. Please try again shortly.",
                "role": "model",
                "type": "rate_limit",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)

        except ServiceUnavailableError as e:
            logger.warning(f"Service unavailable: {str(e)}")
            error_data = {
                "event": "error",
                "content": "Service is temporarily unavailable. Please retry.",
                "role": "model",
                "type": "unavailable",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)

        except Timeout as e:
            logger.warning(f"Request timed out: {str(e)}")
            error_data = {
                "event": "error",
                "content": "The request timed out. Try again later.",
                "role": "model",
                "type": "timeout",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)

        except InvalidRequestError as e:
            logger.warning(f"Invalid request: {str(e)}")
            error_data = {
                "event": "error",
                "content": "There was a problem with your request.",
                "role": "model",
                "type": "invalid_request",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)

        except APIError as e:
            logger.error(f"General API error: {str(e)}")
            error_data = {
                "event": "error",
                "content": "Unexpected service error. Please try again later.",
                "role": "model",
                "type": "service_error",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)
        except InternalServerError as e:
            logger.error(f"Internal server error: {str(e)}")
            error_data = {
                "event": "error",
                "content": "Overloaded. Please try again later.",
                "role": "model",
                "type": "service_error",
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)

        except Exception as e:
            logger.error(f"Unhandled error during agent execution: {str(e)}")
            error_type = (
                "rate_limit"
                if "rate limit" in str(e).lower() or "capacity" in str(e).lower()
                else "service_error"
            )
            error_data = {
                "event": "error",
                "content": f"{str(e)}",
                "role": "model",
                "type": error_type,
            }
            loop.call_soon_threadsafe(q.put_nowait, error_data)
        finally:
            loop.call_soon_threadsafe(q.put_nowait, STOPPED)  # sentinel

    # each thread needs its own loop
    asyncio.run(_run())


class AgentRunRequest(BaseModel):
    data: Optional[Dict[str, Any]] = None
    message: str
    resource_id: str
    resource_type: str


class TitleResponse(BaseModel):
    title: str
    is_greeting: bool


@router.post("/get_title", response_model=TitleResponse)
async def get_title(message: str = Body(..., embed=True)):
    """Analyze message to determine if it's a greeting and generate a title."""
    try:
        response = completion(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """
                                Analyze the user message. If it's a greeting or small talk, return a JSON response with 'title' set to summary of the greeting or small talk and 'is_greeting' set to true. It should not be in third person for eg:"User greets with "Hey"".
                                Otherwise, generate a concise, descriptive title for the conversation and return a JSON with 'title' set to that title and 'is_greeting' set to false.
                                The title should be in the first person (implied or explicit)
                               """,
                },
                {"role": "user", "content": message},
            ],
            response_format={"type": "json_object"},
            max_tokens=100,
            temperature=0,
        )

        result = json.loads(response.choices[0].message.content)
        return TitleResponse(
            title=result.get("title", "Conversation"),
            is_greeting=result.get("is_greeting", False),
        )
    except Exception as e:
        logger.error(f"Error analyzing message: {str(e)}")
        return TitleResponse(title="Conversation", is_greeting=False)


##Old way of handling events(will be deleted after some debugging)
@router.post("/agents/{name}/{session_id}/chat-old")
async def chat_with_agent(
    request: Request,
    name: str = Path(..., description="Agent name"),
    session_id: str = Path(..., description="Session ID"),
    organization: str = Query(..., description="Organization ID"),
    request_body: AgentRunRequest = Body(..., description="Request data"),
    mode: str = Query(None, description="Mode for the agent: deep_dive, etc"),
) -> StreamingResponse:
    """Chat with a specific agent."""
    user_id = request.state.user_id
    # Validate data using check_data_files_exist
    try:
        if not check_data_files_exist(
            organization,
            name,
            {
                "resource_id": request_body.resource_id,
                "resource_type": request_body.resource_type,
            },
        ):
            raise HTTPException(
                status_code=404,
                detail=f"No data found for organization: {organization} and module: {name}",
            )
    except Exception:
        raise HTTPException(
            status_code=404,
            detail=f"No data found for organization: {organization} and module: {name}",
        )

    logger.info(
        f"Validated organization: {organization} and user_id: {user_id}, {request_body.model_dump_json(exclude_none=True, by_alias=True)}"
    )

    # Get the agent
    agent = agents.get_agent({
        "module_name": name,
        "resource_type": request_body.resource_type,
        "resource_id": request_body.resource_id,
        "data": request_body.data or {},
        "mode": mode,
    })
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    # Session management
    session_service = SessionService
    user_id = f"{user_id}|{request_body.resource_type}|{request_body.resource_id}"
    app_name = f"{name}_agent"
    runner = Runner(app_name=app_name, agent=agent, session_service=session_service)
    # Check if session exists
    session_exists = True
    if (
        session_service.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )
        is None
    ):
        session_exists = False
        ## Saving the state, so that tools can access it. Sample implementation is present in terra_agent
        session_service.create_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            state={
                "additional_request_data": {
                    **json.loads(
                        request_body.model_dump_json(exclude_none=True, by_alias=True)
                    ),
                    "organization": organization,
                    "module_name": name,
                }
            },
        )

    session = session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Handle title generation and greeting detection
    session_state = session.state or {}
    is_greeting = session_state.get("is_greeting", None)

    # If this is a new session or we previously detected a greeting
    if not session_exists or is_greeting is True:
        # Call the title API
        title_response = await get_title(message=request_body.message)

        # Update session state with title info
        session_state["title"] = title_response.title
        session_state["is_greeting"] = title_response.is_greeting
        current_time = time.time()
        # --- Create Event with Actions ---
        actions_with_update = EventActions(state_delta=session_state)
        # This event might represent an internal system action, not just an agent response
        system_event = Event(
            invocation_id="greeting_update",
            author="system",  # Or 'agent', 'tool' etc.
            actions=actions_with_update,
            timestamp=current_time,
        )
        await session_service.append_event(session, system_event)

        logger.info(
            f"Updated session with title: {title_response.title}, is_greeting: {title_response.is_greeting}"
        )

    content = types.Content(role="user", parts=[types.Part(text=request_body.message)])

    # Event generator function
    async def event_generator():
        # Create the agent event stream
        try:
            async for event in runner.run_async(
                user_id=user_id,
                session_id=session.id,
                new_message=content,
                run_config=RunConfig(streaming_mode="sse"),
            ):
                # ## Processed event
                # processed_event = process_agent_events(event)
                # yield f"data: {json.dumps(processed_event)}\n\n"
                ## Raw event
                log_pretty("Event", event.content)
                log_pretty("Timestamp", time.strftime("%H:%M:%S", time.localtime()))
                sse_event = event.model_dump_json(exclude_none=True, by_alias=True)
                yield f"data: {sse_event}\n\n"

        except RateLimitError as e:
            logger.warning(f"Rate limit hit: {str(e)}")
            yield f"data: {json.dumps({'event': 'error', 'content': 'Rate limit exceeded. Please try again shortly.', 'role': 'model', 'type': 'rate_limit'})}\n\n"

        except ServiceUnavailableError as e:
            logger.warning(f"Service unavailable: {str(e)}")
            yield f"data: {json.dumps({'event': 'error', 'content': 'Service is temporarily unavailable. Please retry.', 'role': 'model', 'type': 'unavailable'})}\n\n"

        except Timeout as e:
            logger.warning(f"Request timed out: {str(e)}")
            yield f"data: {json.dumps({'event': 'error', 'content': 'The request timed out. Try again later.', 'role': 'model', 'type': 'timeout'})}\n\n"

        except InvalidRequestError as e:
            logger.warning(f"Invalid request: {str(e)}")
            yield f"data: {json.dumps({'event': 'error', 'content': 'There was a problem with your request.', 'role': 'model', 'type': 'invalid_request'})}\n\n"

        except APIError as e:
            logger.error(f"General API error: {str(e)}")
            yield f"data: {json.dumps({'event': 'error', 'content': 'Unexpected service error. Please try again later.', 'role': 'model', 'type': 'service_error'})}\n\n"

        except Exception as e:
            logger.error(f"Unhandled error during agent execution: {str(e)}")
            error_type = (
                "rate_limit"
                if "rate limit" in str(e).lower() or "capacity" in str(e).lower()
                else "service_error"
            )
            yield f"data: {json.dumps({'event': 'error', 'content': f'Service error: {str(e)}', 'role': 'model', 'type': error_type})}\n\n"

    # Return streaming response
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
    )


@router.post("/agents/{name}/{session_id}/chat/")
async def chat(
    request: Request,
    name: str = Path(..., description="Agent name"),
    session_id: str = Path(..., description="Session ID"),
    organization: str = Query(..., description="Organization ID"),
    request_body: AgentRunRequest = Body(..., description="Request data"),
    mode: str = Query(None, description="Mode for the agent: deep_dive, etc"),
):
    """
    SSE endpoint. Starts one thread-pool job and streams its queue.
    """
    """Chat with a specific agent."""
    user_id = request.state.user_id
    # Validate data using check_data_files_exist. Middleware is disabled for now.
    try:
        if not check_data_files_exist(
            organization,
            name,
            {
                "resource_id": request_body.resource_id,
                "resource_type": request_body.resource_type,
            },
        ):
            raise HTTPException(
                status_code=404,
                detail=f"No data found for organization: {organization} and module: {name}",
            )
    except Exception:
        raise HTTPException(
            status_code=404,
            detail=f"No data found for organization: {organization} and module: {name}",
        )

    logger.info(
        f"Validated organization: {organization} and user_id: {user_id}, {request_body.model_dump_json(exclude_none=True, by_alias=True)}"
    )

    # Get the agent
    agent = agents.get_agent({
        "module_name": name,
        "resource_type": request_body.resource_type,
        "resource_id": request_body.resource_id,
        "data": request_body.data or {},
        "organization": organization,
        "mode": mode,
    })
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    # Session management
    session_service = SessionService
    user_id = f"{user_id}|{request_body.resource_type}|{request_body.resource_id}"
    app_name = f"{name}_agent"
    runner = Runner(app_name=app_name, agent=agent, session_service=session_service)
    # Check if session exists
    session_exists = True
    if (
        await session_service.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )
        is None
    ):
        session_exists = False
        ## Saving the state, so that tools can access it. Sample implementation is present in terra_agent
        await session_service.create_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            state={
                "additional_request_data": {
                    **json.loads(
                        request_body.model_dump_json(exclude_none=True, by_alias=True)
                    ),
                    "organization": organization,
                    "module_name": name,
                }
            },
        )

    session = await session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Handle title generation and greeting detection
    session_state = session.state or {}
    is_greeting = session_state.get("is_greeting", None)

    # If this is a new session or we previously detected a greeting
    if not session_exists or is_greeting is True:
        # Call the title API
        title_response = await get_title(message=request_body.message)

        # Update session state with title info
        session_state["title"] = title_response.title
        session_state["is_greeting"] = title_response.is_greeting
        current_time = time.time()
        # --- Create Event with Actions ---
        actions_with_update = EventActions(state_delta=session_state)
        system_event = Event(
            invocation_id="greeting_update",
            author="system",
            actions=actions_with_update,
            timestamp=current_time,
        )
        await session_service.append_event(session, system_event)

        logger.info(
            f"Updated session with title: {title_response.title}, is_greeting: {title_response.is_greeting}"
        )
    session_state["initial_query"] = True
    current_time = time.time()
    actions_with_update = EventActions(state_delta=session_state)
    system_event = Event(
        invocation_id="initial_query_update",
        author="system",
        actions=actions_with_update,
        timestamp=current_time,
    )
    await session_service.append_event(session, system_event)
    queue: asyncio.Queue = asyncio.Queue(maxsize=QUEUE_SIZE)
    loop = asyncio.get_event_loop()
    stop_flag = threading.Event()
    session = await SessionService.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    ids_to_delete = []
    found_terminal_event = False
    # Iterate through events in reverse order
    for event in reversed(session.events):
        if event.content is not None:
            part = event.content.parts[0]
            if part.function_call and not found_terminal_event:
                ids_to_delete.append(event.id)
            elif part.function_response or part.text:
                found_terminal_event = True
                break  # We found a text or response, no need to delete further
    logger.info(f"Ids to delete: {ids_to_delete}")
    if ids_to_delete:
        deleted_count = await SessionService.delete_events_by_ids(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            event_ids=ids_to_delete,
        )
        logger.info(f"Deleted {deleted_count} function_call events.")
    executor.submit(
        adk_worker,
        session_id,
        request_body.message,
        loop,
        queue,
        runner,
        user_id,
        stop_flag,
    )

    print("session.state before: ", session.state)

    # Event generator function
    async def event_generator():
        try:
            while True:
                try:
                    # pull next chunk or time-out for keep-alive
                    item = await asyncio.wait_for(queue.get(), timeout=HEARTBEAT)
                except asyncio.TimeoutError:
                    # connection still alive → send a comment
                    yield ": keep-alive\n\n"
                    continue

                if item is STOPPED or item is None:
                    # background worker is finished
                    break
                session = await SessionService.get_session(
                    app_name=app_name, user_id=user_id, session_id=session_id
                )
                await update_usage_metadata(item, session, session_service)
                yield f"data: {json.dumps(item)}\n\n"

        except (
            asyncio.CancelledError
        ):  # ← THIS triggers the moment the browser/tab disconnects
            # make absolutely sure producer thread exits
            log_pretty("Cancelled", "Exiting")
            stop_flag.set()
        finally:
            # for any other reason we exit
            print("session.state at last: ", session.state)
            stop_flag.set()

    # Return streaming response
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
    )


@router.get("/agents/{module_name}/sessions/")
async def get_sessions(
    request: Request,
    module_name: str = Path(..., description="Module name"),
    resource_type: str = Query(..., description="Resource type"),
    resource_id: str = Query(..., description="Resource ID"),
):
    """Get all sessions for a specific module."""
    try:
        user_id = request.state.user_id
        user_id = f"{user_id}|{resource_type}|{resource_id}"
        app_name = f"{module_name}_agent"

        # Get sessions list
        sessions = await SessionService.list_sessions(
            app_name=app_name, user_id=user_id
        )

        if not sessions or not hasattr(sessions, "sessions"):
            return {"sessions": []}

        # Doing this as the list_sessions does not return the state and events
        for session in sessions.sessions:
            complete_session_details = await SessionService.get_session(
                app_name=app_name, user_id=user_id, session_id=session.id
            )
            if complete_session_details:
                session.state = complete_session_details.state

        return sessions

    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve sessions")


@router.get("/agents/{module_name}/sessions/{session_id}/")
async def get_session(
    request: Request,
    module_name: str = Path(..., description="Module name"),
    session_id: str = Path(..., description="Session ID"),
    resource_type: str = Query(..., description="Resource type"),
    resource_id: str = Query(..., description="Resource ID"),
):
    """Get a specific session for a specific module."""
    try:
        user_id = request.state.user_id
        user_id = f"{user_id}|{resource_type}|{resource_id}"
        app_name = f"{module_name}_agent"

        # Get the session from the service
        session = await SessionService.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )

        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Process the session's events
        processed_session = process_session_events(session)
        return processed_session

    except HTTPException:
        # Re-raise HTTP exceptions (like 404) as is
        raise
    except Exception as e:
        logger.error(f"Error getting session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve session")


@router.get("/agents/{module_name}/initialize/")
def check_if_data_exists(
    module_name: str = Path(..., description="Agent name"),
    organization: str = Query(..., description="Organization ID"),
    resource_type: str = Query(..., description="Resource type"),
    resource_id: str = Query(..., description="Resource ID"),
):
    """Check if the data exists"""
    # Validate data using check_data_files_exist
    try:
        if not check_data_files_exist(
            organization,
            module_name,
            request={"resource_type": resource_type, "resource_id": resource_id},
        ):
            raise HTTPException(
                status_code=404,
                detail=f"No data found for organization: {organization} and module: {module_name}",
            )
        else:
            return {"status": "success"}
    except Exception:
        raise HTTPException(
            status_code=404,
            detail=f"No data found for organization: {organization} and module: {module_name}",
        )


@router.get("/suggestions/{module_name}/{session_id}/")
async def get_suggestions(
    request: Request,
    module_name: str = Path(..., description="Module name"),
    session_id: str = Path(..., description="Session ID"),
    resource_type: str = Query(..., description="Resource type"),
    resource_id: str = Query(..., description="Resource ID"),
):
    """Get suggestions based on a session."""
    try:
        user_id = request.state.user_id
        user_id = f"{user_id}|{resource_type}|{resource_id}"
        app_name = f"{module_name}_agent"

        session = await SessionService.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )

        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        return get_related_questions(session, module_name)

    except HTTPException:
        # Re-raise HTTP exceptions (like 404) as is
        raise
    except Exception as e:
        logger.error(f"Error getting suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve suggestions")
