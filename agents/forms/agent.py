from pathlib import Path

from dotenv import load_dotenv
from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

from .utils.prompts import TEMPLATE_CREATION_PROMPT
from .utils.tools import think, sql, display_chart, list_forms
from .utils.helpers import construct_prompt

# Load environment variables from .env and .env.local file
# Load default .env first (usually in the current or parent directory)
load_dotenv()

# Determine project root and load .env.local with override
# Assumes this script is nested two levels below the root where .env.local is
try:
    root_dir = Path(__file__).resolve().parent.parent.parent
    env_local_path = root_dir / ".env.local"
    if env_local_path.exists():
        load_dotenv(dotenv_path=env_local_path, override=True)
        print(f"Loaded environment variables from: {env_local_path}")
    else:
        print(f".env.local not found at expected location: {env_local_path}")
except Exception as e:
    print(f"Error determining root directory or loading .env.local: {e}")


def get_agent(metadata):
    ## metadata will have resource_type and resource_id as well to decide which agent to use if multi agent in the same module, and dynamic prompts

    if metadata["resource_type"] == "template_retriever":
        # Define the list of tools available to the agent
        tools = [sql, think, display_chart, list_forms]

        system_prompt_for_form_tempalte = construct_prompt(metadata)

        config = {
            "model": LiteLlm(
                model="anthropic/claude-3-5-sonnet-latest",
                parallel_tool_calls=False,
                temperature=0,
                drop_params=True,
                stream_options={"include_usage": True},
            ),
            "name": "forms_template_retriever_agent",
            "instruction": system_prompt_for_form_tempalte,
            "tools": tools,
        }
        return LlmAgent(**config)
    else:
        config = {
            "model": LiteLlm(
                model="gpt-4.1",
                parallel_tool_calls=False,
                temperature=0,
                drop_params=True,
                stream_options={"include_usage": True},
            ),
            "name": "forms_template_creation_agent",
            "instruction": TEMPLATE_CREATION_PROMPT,
            "tools": [],
        }
        return LlmAgent(**config)

root_agent = get_agent({"resource_type": "template_retriever", "resource_id": "rPQUE49Hh", "organization": "org_id_1"})